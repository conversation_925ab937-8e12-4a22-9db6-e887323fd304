"use client"

import React, { useCallback } from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useDemoTour } from "@/lib/domains/user/user.hooks"
import { toast } from "@/components/ui/use-toast"

interface DemoTourModalProps {
  isOpen: boolean
  onClose: () => void
}

export function DemoTourModal({ isOpen, onClose }: DemoTourModalProps) {
  const { optOutOfDemoTour } = useDemoTour()

  const handleOptOut = useCallback(async () => {
    try {
      const success = await optOutOfDemoTour()
      if (success) {
        toast({
          title: "Demo tour disabled",
          description: "You won't see this tour again. You can always access it from the sidebar.",
        })
        onClose()
      } else {
        toast({
          title: "Error",
          description: "Failed to save preference. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error opting out of demo tour:", error)
      toast({
        title: "Error",
        description: "Failed to save preference. Please try again.",
        variant: "destructive",
      })
    }
  }, [optOutOfDemoTour, onClose])

  const handleTakeTour = useCallback(() => {
    // User is taking the tour, so we consider this as "viewed" and opt them out optimistically
    optOutOfDemoTour()
    // Keep the modal open so they can view the tour
  }, [optOutOfDemoTour])

  const handleClose = useCallback(() => {
    // If user clicks close button, treat it as "viewed" and opt them out optimistically
    optOutOfDemoTour()
    onClose()
  }, [optOutOfDemoTour, onClose])

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-none w-screen h-screen p-0 m-0 border-0 rounded-none">
        {/* Add DialogTitle and DialogDescription for accessibility - visually hidden */}
        <DialogHeader className="sr-only">
          <DialogTitle>Togeda.ai Demo Tour</DialogTitle>
          <DialogDescription>
            Interactive demo tour showing how to use Togeda.ai to plan epic trips with your squads
          </DialogDescription>
        </DialogHeader>

        {/* Header with close button and action buttons */}
        <div className="absolute top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b p-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold">Welcome to Togeda.ai!</h2>
            <p className="text-sm text-muted-foreground">
              Take a quick tour to see how to plan epic trips with your squads
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleOptOut}>
              Do not show this again
            </Button>
            <Button onClick={handleTakeTour}>Take the Tour</Button>
            <Button variant="ghost" size="icon" onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Iframe container */}
        <div className="pt-20 h-full">
          <div
            style={{
              position: "relative",
              boxSizing: "content-box",
              maxHeight: "80svh",
              width: "100%",
              aspectRatio: "1.718362282878412",
              padding: "40px 0 40px 0",
            }}
          >
            <iframe
              src="https://app.supademo.com/embed/cmebvnofa6dc0h3py1ptxpsdm?embed_v=2&utm_source=embed"
              loading="lazy"
              title="Use Togeda.ai - Plan Your Next Adventure  to Plan Epic Trips With Your Squads!"
              allow="clipboard-write; fullscreen; autoplay; encrypted-media"
              allowFullScreen
              className="border-0"
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
              }}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
