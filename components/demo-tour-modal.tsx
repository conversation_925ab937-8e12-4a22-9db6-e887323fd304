"use client"

import React, { use<PERSON><PERSON>back, useState } from "react"
import { <PERSON>, <PERSON>, <PERSON>rk<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useDemoTour } from "@/lib/domains/user/user.hooks"
import { toast } from "@/components/ui/use-toast"

interface DemoTourModalProps {
  isOpen: boolean
  onClose: () => void
}

export function DemoTourModal({ isOpen, onClose }: DemoTourModalProps) {
  const { optOutOfDemoTour } = useDemoTour()
  const [showVideo, setShowVideo] = useState(false)

  const handleOptOut = useCallback(async () => {
    try {
      const success = await optOutOfDemoTour()
      if (success) {
        toast({
          title: "Demo tour disabled",
          description: "You won't see this tour again. You can always access it from the sidebar.",
        })
        onClose()
      } else {
        toast({
          title: "Error",
          description: "Failed to save preference. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error opting out of demo tour:", error)
      toast({
        title: "Error",
        description: "Failed to save preference. Please try again.",
        variant: "destructive",
      })
    }
  }, [optOutOfDemoTour, onClose])

  const handleTakeTour = useCallback(() => {
    // Show the video iframe
    setShowVideo(true)
    // Opt out so they don't see the confirmation again
    optOutOfDemoTour()
  }, [optOutOfDemoTour])

  const handleClose = useCallback(() => {
    // If user clicks close button, treat it as "viewed" and opt them out optimistically
    optOutOfDemoTour()
    onClose()
  }, [optOutOfDemoTour, onClose])

  const handleVideoClose = useCallback(() => {
    // Close the video and the entire modal
    setShowVideo(false)
    onClose()
  }, [onClose])

  if (showVideo) {
    // Show fullscreen video iframe
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && handleVideoClose()}>
        <DialogContent className="max-w-none w-screen h-screen p-0 m-0 border-0 rounded-none">
          {/* Add DialogTitle and DialogDescription for accessibility - visually hidden */}
          <DialogHeader className="sr-only">
            <DialogTitle>Togeda.ai Demo Tour Video</DialogTitle>
            <DialogDescription>
              Interactive demo tour showing how to use Togeda.ai to plan epic trips with your squads
            </DialogDescription>
          </DialogHeader>

          {/* Header with close button */}
          <div className="absolute top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b p-4 flex justify-between items-center">
            <div className="flex items-center gap-4">
              <h2 className="text-lg font-semibold">Togeda.ai Demo Tour</h2>
              <p className="text-sm text-muted-foreground">
                Learn how to plan epic trips with your squads
              </p>
            </div>
            <Button variant="ghost" size="icon" onClick={handleVideoClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Iframe container */}
          <div className="pt-20 h-full">
            <div
              style={{
                position: "relative",
                boxSizing: "content-box",
                maxHeight: "80svh",
                width: "100%",
                aspectRatio: "1.718362282878412",
                padding: "40px 0 40px 0",
              }}
            >
              <iframe
                src="https://app.supademo.com/embed/cmebvnofa6dc0h3py1ptxpsdm?embed_v=2&utm_source=embed"
                loading="lazy"
                title="Use Togeda.ai - Plan Your Next Adventure  to Plan Epic Trips With Your Squads!"
                allow="clipboard-write; fullscreen; autoplay; encrypted-media"
                allowFullScreen
                className="border-0"
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100%",
                }}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  // Show confirmation dialog
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            Welcome to Togeda.ai!
          </DialogTitle>
          <DialogDescription>
            Would you like to take a quick tour to see how to plan epic trips with your squads?
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-3 pt-4">
          <Button onClick={handleTakeTour} className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            Take the Tour
          </Button>
          <Button variant="outline" onClick={handleOptOut}>
            Do not show this again
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
